{"name": "seedlog", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite --host --port 5008", "dev:workers": "wrangler dev", "build": "npm run type-check && vite build --outDir dist/client && wrangler pages functions build --outdir=./dist/_worker.js/", "preview": "npm run build && wrangler dev", "test:unit": "vitest", "type-check": "vue-tsc --build", "deploy": "npm run build && wrangler deploy", "push": "drizzle-kit push --config=drizzle-dev.drizzle.config.ts", "generate": "drizzle-kit generate --config=drizzle-prod.drizzle.config.ts", "migrate:local": "wrangler d1 migrations apply seedlog --local", "migrate:remote": "wrangler d1 migrations apply seedlog --remote"}, "dependencies": {"@libsql/client": "^0.14.0", "@monaco-editor/loader": "^1.4.0", "@tailwindcss/vite": "^4.0.0", "dotenv": "^16.4.7", "drizzle-orm": "^0.40.0", "moment-timezone": "^0.5.47", "monaco-editor": "^0.52.2", "pinia": "^2.3.0", "tailwindcss": "^4.0.0", "tdesign-vue-next": "^1.10.7", "uuid": "^11.0.5", "vue": "^3.5.13", "vue-router": "^4.5.0"}, "devDependencies": {"@cloudflare/workers-types": "^4.20250124.3", "@tsconfig/node22": "^22.0.0", "@types/jsdom": "^21.1.7", "@types/node": "^22.10.2", "@vitejs/plugin-vue": "^5.2.1", "@vue/test-utils": "^2.4.6", "@vue/tsconfig": "^0.7.0", "drizzle-kit": "^0.30.5", "jsdom": "^25.0.1", "npm-run-all2": "^7.0.2", "tsx": "^4.19.3", "typescript": "~5.6.3", "unplugin-auto-import": "^19.0.0", "unplugin-vue-components": "^28.0.0", "vite": "^6.0.5", "vite-plugin-vue-devtools": "^7.6.8", "vitest": "^2.1.8", "vue-tsc": "^2.1.10", "wrangler": "^4.26.1"}}