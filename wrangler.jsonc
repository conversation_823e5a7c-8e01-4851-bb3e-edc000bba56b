{
	// 从 wrangler.toml 迁移到 Workers 格式生成
	"name": "seedlog",
	"compatibility_date": "2025-01-27",
	"compatibility_flags": ["nodejs_compat_v2"],

	// Workers 配置与静态资源
	"main": "./dist/_worker.js",
	"assets": {
		"directory": "./dist",
		"binding": "ASSETS"
	},

	// 开发环境
	"vars": {}, // 不要在这里写入实际值，使用 .dev.vars
	"kv_namespaces": [
		{ "binding": "KV", "id": "seedlog-local" }
	],
	"d1_databases": [
		{
			"binding": "DB",
			"database_name": "seedlog",
			"database_id": "54e16795-ed28-41ac-96c8-211a2a431a29",
			"migrations_dir": "drizzle",
			"preview_database_id": "seedlog"
		}
	],
	"ai": {
		"binding": "AI"
	},

	// 环境配置
	"env": {
		"preview": {
			"vars": {}, // 不要在这里写入实际值，使用 .dev.vars
			"kv_namespaces": [
				{ "binding": "KV", "id": "d49cb3b21e9948fb8e6debbe255473c4" }
			]
		},
		"production": {
			"vars": {}, // 不要在这里写入实际值，在网站上设置
			"kv_namespaces": [
				{ "binding": "KV", "id": "d49cb3b21e9948fb8e6debbe255473c4" }
			],
			"ai": {
				"binding": "AI"
			},
			"d1_databases": [
				{
					"binding": "DB",
					"database_name": "seedlog",
					"database_id": "54e16795-ed28-41ac-96c8-211a2a431a29",
					"migrations_dir": "drizzle"
				}
			]
		}
	}
}
